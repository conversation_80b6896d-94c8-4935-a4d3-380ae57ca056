"""
Azure Blob Storage - Delete Vendor Folders and Contents

This script deletes vendor folders and all their contents from Azure Blob Storage.
Structure: data/Repository/{vendor_name}/{pdf,ocr,text}/files
"""

import os
import logging
from typing import List, Optional
from azure.storage.blob import BlobServiceClient, BlobClient, ContainerClient
from azure.core.exceptions import ResourceNotFoundError, AzureError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AzureBlobDeleter:
    """Class to handle deletion of vendor folders from Azure Blob Storage"""
    
    def __init__(self, connection_string: str, container_name: str = "contract-assist"):
        """
        Initialize the Azure Blob Deleter

        Args:
            connection_string: Azure Storage connection string
            container_name: Name of the container (default: "contract-assist")
        """
        self.connection_string = connection_string
        self.container_name = container_name
        self.blob_service_client = BlobServiceClient.from_connection_string(connection_string)
        self.container_client = self.blob_service_client.get_container_client(container_name)
        
    def list_vendor_folders(self, base_path: str = "Repository/") -> List[str]:
        """
        List all vendor folders in the specified path
        
        Args:
            base_path: Base path where vendor folders are located
            
        Returns:
            List of vendor folder names
        """
        vendor_folders = set()
        
        try:
            # List all blobs with the base path prefix
            blob_list = self.container_client.list_blobs(name_starts_with=base_path)
            
            for blob in blob_list:
                # Extract vendor folder name from blob path
                # Example: Repository/ABC Limited/Ocr/file.pdf -> ABC Limited
                relative_path = blob.name[len(base_path):]
                if relative_path and '/' in relative_path:
                    vendor_name = relative_path.split('/')[0]
                    vendor_folders.add(vendor_name)
                    
        except Exception as e:
            logger.error(f"Error listing vendor folders: {str(e)}")
            
        return list(vendor_folders)
    
    def delete_vendor_folder(self, vendor_name: str, base_path: str = "Repository/", dry_run: bool = False) -> bool:
        """
        Delete a specific vendor folder and all its contents

        Args:
            vendor_name: Name of the vendor folder to delete
            base_path: Base path where vendor folders are located
            dry_run: If True, only show what would be deleted without actually deleting

        Returns:
            True if deletion was successful, False otherwise
        """
        vendor_path = f"{base_path}{vendor_name}/"
        deleted_count = 0

        try:
            action = "Would delete" if dry_run else "Starting deletion of"
            logger.info(f"{action} vendor folder: {vendor_name}")

            # List all blobs in the vendor folder
            blob_list = self.container_client.list_blobs(name_starts_with=vendor_path)
            blobs_to_process = list(blob_list)

            if not blobs_to_process:
                logger.warning(f"No files found in vendor folder: {vendor_name}")
                return True

            logger.info(f"Found {len(blobs_to_process)} files in vendor folder: {vendor_name}")

            # Delete each blob (or just log if dry run)
            for blob in blobs_to_process:
                try:
                    if dry_run:
                        logger.info(f"Would delete: {blob.name}")
                        deleted_count += 1
                    else:
                        self.container_client.delete_blob(blob.name)
                        deleted_count += 1
                        logger.debug(f"Deleted blob: {blob.name}")
                except ResourceNotFoundError:
                    logger.warning(f"Blob not found (may have been already deleted): {blob.name}")
                except Exception as e:
                    logger.error(f"Error deleting blob {blob.name}: {str(e)}")

            action_past = "Would have deleted" if dry_run else "Successfully deleted"
            logger.info(f"{action_past} {deleted_count} files from vendor folder: {vendor_name}")
            return True

        except Exception as e:
            logger.error(f"Error processing vendor folder {vendor_name}: {str(e)}")
            return False
    
    def delete_multiple_vendors(self, vendor_names: List[str], base_path: str = "Repository/", dry_run: bool = False) -> dict:
        """
        Delete multiple vendor folders

        Args:
            vendor_names: List of vendor folder names to delete
            base_path: Base path where vendor folders are located
            dry_run: If True, only show what would be deleted without actually deleting

        Returns:
            Dictionary with deletion results for each vendor
        """
        results = {}

        for vendor_name in vendor_names:
            results[vendor_name] = self.delete_vendor_folder(vendor_name, base_path, dry_run)

        return results

    def delete_all_vendors(self, base_path: str = "Repository/", dry_run: bool = False) -> dict:
        """
        Delete all vendor folders in the specified path

        Args:
            base_path: Base path where vendor folders are located
            dry_run: If True, only show what would be deleted without actually deleting

        Returns:
            Dictionary with deletion results for each vendor
        """
        vendor_folders = self.list_vendor_folders(base_path)

        if not vendor_folders:
            logger.info("No vendor folders found to delete")
            return {}

        logger.info(f"Found {len(vendor_folders)} vendor folders: {vendor_folders}")

        return self.delete_multiple_vendors(vendor_folders, base_path, dry_run)


def main():
    """Main function to demonstrate usage"""

    # Try to get connection string from environment variable first
    CONNECTION_STRING = os.getenv('AZURE_STORAGE_CONNECTION_STRING')

    # If not found, try to load from config file
    if not CONNECTION_STRING:
        try:
            import config_example as config
            CONNECTION_STRING = config.AZURE_STORAGE_CONNECTION_STRING
            CONTAINER_NAME = config.CONTAINER_NAME
            BASE_PATH = config.BASE_PATH
            logger.info("Loaded configuration from config_example.py")
        except ImportError:
            logger.error("Please set AZURE_STORAGE_CONNECTION_STRING environment variable or create config_example.py")
            return
        except AttributeError as e:
            logger.error(f"Missing configuration in config_example.py: {e}")
            return
    else:
        # Use default values if using environment variable
        CONTAINER_NAME = "contract-assist"
        BASE_PATH = "Repository/"

    if not CONNECTION_STRING:
        logger.error("No connection string found. Please set AZURE_STORAGE_CONNECTION_STRING environment variable or update config_example.py")
        return
    
    try:
        # Initialize the deleter
        deleter = AzureBlobDeleter(CONNECTION_STRING, CONTAINER_NAME)
        
        # Option 1: List vendor folders first
        print("Available vendor folders:")
        vendor_folders = deleter.list_vendor_folders(BASE_PATH)
        for vendor in vendor_folders:
            print(f"  - {vendor}")
        
        if not vendor_folders:
            print("No vendor folders found.")
            return
        
        # First, do a dry run to show what would be deleted
        print(f"\n--- DRY RUN: Showing what would be deleted ---")
        deleter.delete_all_vendors(BASE_PATH, dry_run=True)

        # Confirm deletion
        response = input(f"\nDo you want to proceed with deleting all {len(vendor_folders)} vendor folders? (yes/no): ")

        if response.lower() in ['yes', 'y']:
            # Option 2: Delete all vendor folders
            print(f"\n--- ACTUAL DELETION ---")
            results = deleter.delete_all_vendors(BASE_PATH, dry_run=False)

            print("\nDeletion Results:")
            for vendor, success in results.items():
                status = "SUCCESS" if success else "FAILED"
                print(f"  {vendor}: {status}")
        else:
            print("Deletion cancelled.")
            
        # Option 3: Delete specific vendors (uncomment to use)
        # specific_vendors = ["Tcs", "tata Steel"]
        # results = deleter.delete_multiple_vendors(specific_vendors, BASE_PATH)
        
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")


if __name__ == "__main__":
    main()
