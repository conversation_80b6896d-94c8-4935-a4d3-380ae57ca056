"""
Configuration example for Azure Blob Storage deletion script

Copy this file to config.py and update with your actual values
"""

# Azure Storage Configuration
AZURE_STORAGE_CONNECTION_STRING = "DefaultEndpointsProtocol=https;AccountName=contractassist;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"

# Container and path configuration
CONTAINER_NAME = "contract-assist"
BASE_PATH = "Repository/"

# Vendor folders to delete (if you want to specify particular ones)
SPECIFIC_VENDORS = [
    "ABC Limited",
    "Tata Steel",
    "TCS"
]

# Example usage configurations
DELETE_ALL_VENDORS = True  # Set to False if you want to delete only specific vendors
DRY_RUN = False  # Set to True to see what would be deleted without actually deleting
