# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) AutoRest Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
# pylint: disable=wrong-import-position

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ._patch import *  # pylint: disable=unused-wildcard-import

from ._service_operations import ServiceOperations  # type: ignore
from ._container_operations import ContainerOperations  # type: ignore
from ._blob_operations import BlobOperations  # type: ignore
from ._page_blob_operations import PageBlobOperations  # type: ignore
from ._append_blob_operations import AppendBlobOperations  # type: ignore
from ._block_blob_operations import BlockBlobOperations  # type: ignore

from ._patch import __all__ as _patch_all
from ._patch import *
from ._patch import patch_sdk as _patch_sdk

__all__ = [
    "ServiceOperations",
    "ContainerOperations",
    "BlobOperations",
    "PageBlobOperations",
    "AppendBlobOperations",
    "BlockBlobOperations",
]
__all__.extend([p for p in _patch_all if p not in __all__])  # pyright: ignore
_patch_sdk()
