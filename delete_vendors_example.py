"""
Example script showing different ways to use the Azure Blob Deleter

Before running this script:
1. Install dependencies: pip install -r requirements.txt
2. Set your Azure Storage connection string as an environment variable:
   export AZURE_STORAGE_CONNECTION_STRING="your_connection_string_here"
"""

import os
from azure_blob_delete import AzureBlobDeleter

def main():
    # Get connection string from environment variable
    connection_string = os.getenv('AZURE_STORAGE_CONNECTION_STRING')
    
    if not connection_string:
        print("Error: Please set AZURE_STORAGE_CONNECTION_STRING environment variable")
        print("Example: export AZURE_STORAGE_CONNECTION_STRING='DefaultEndpointsProtocol=https;AccountName=...'")
        return
    
    # Initialize the deleter
    container_name = "contract-assist"
    base_path = "Repository/"
    
    deleter = AzureBlobDeleter(connection_string, container_name)
    
    print("=== Azure Blob Storage Vendor Folder Deletion ===\n")
    
    # Example 1: List all vendor folders
    print("1. Listing all vendor folders:")
    vendor_folders = deleter.list_vendor_folders(base_path)
    
    if vendor_folders:
        for vendor in vendor_folders:
            print(f"   - {vendor}")
    else:
        print("   No vendor folders found.")
        return
    
    print(f"\nFound {len(vendor_folders)} vendor folders.\n")
    
    # Example 2: Delete specific vendor folders
    print("2. Example: Delete specific vendors")
    specific_vendors = ["ABC Limited", "TCS"]  # Modify this list as needed
    
    # Check which of the specific vendors actually exist
    existing_vendors = [v for v in specific_vendors if v in vendor_folders]
    
    if existing_vendors:
        print(f"Vendors to delete: {existing_vendors}")
        
        # Dry run first
        print("\n--- DRY RUN for specific vendors ---")
        deleter.delete_multiple_vendors(existing_vendors, base_path, dry_run=True)
        
        response = input(f"\nProceed with deleting {existing_vendors}? (yes/no): ")
        if response.lower() in ['yes', 'y']:
            results = deleter.delete_multiple_vendors(existing_vendors, base_path, dry_run=False)
            print("\nResults:")
            for vendor, success in results.items():
                status = "SUCCESS" if success else "FAILED"
                print(f"  {vendor}: {status}")
        else:
            print("Deletion cancelled.")
    else:
        print(f"None of the specified vendors {specific_vendors} were found.")
    
    print("\n" + "="*50)
    
    # Example 3: Delete all vendor folders (commented out for safety)
    print("3. To delete ALL vendor folders, uncomment the code below:")
    print("   # Dry run first")
    print("   # deleter.delete_all_vendors(base_path, dry_run=True)")
    print("   # response = input('Proceed with deleting ALL vendors? (yes/no): ')")
    print("   # if response.lower() in ['yes', 'y']:")
    print("   #     results = deleter.delete_all_vendors(base_path, dry_run=False)")
    
    
    # Uncomment this section to enable deletion of ALL vendor folders
    print("\n3. Delete ALL vendor folders:")

    # Dry run first
    print("--- DRY RUN for all vendors ---")
    deleter.delete_all_vendors(base_path, dry_run=True)

    response = input(f"\nProceed with deleting ALL {len(vendor_folders)} vendors? (yes/no): ")
    if response.lower() in ['yes', 'y']:
        results = deleter.delete_all_vendors(base_path, dry_run=False)
        print("\nResults:")
        for vendor, success in results.items():
            status = "SUCCESS" if success else "FAILED"
            print(f"  {vendor}: {status}")
    else:
        print("Deletion cancelled.")
    

if __name__ == "__main__":
    main()
