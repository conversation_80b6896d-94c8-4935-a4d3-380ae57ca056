"""
<PERSON>ript to delete specific vendor folders from Azure Blob Storage

This script deletes the following vendor folders and all their contents:
- ABC Limited
- TCS  
- Tata Steel

Each vendor folder contains subfolders: Ocr, texts, uploads
Files can be: .pdf, .txt, .json, .py (but we delete everything regardless of extension)

Container: contract-assist
Path: Repository/{vendor_name}/
"""

import os
import logging
from azure_blob_delete import AzureBlobDeleter

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Delete specific vendor folders"""
    
    # Configuration
    CONNECTION_STRING = os.getenv('AZURE_STORAGE_CONNECTION_STRING')
    CONTAINER_NAME = "contract-assist"
    BASE_PATH = "Repository/"
    
    # Specific vendors to delete
    VENDORS_TO_DELETE = [
        "ABC Limited",
        "TCS", 
        "Tata Steel"
    ]
    
    if not CONNECTION_STRING:
        print("❌ Error: Please set AZURE_STORAGE_CONNECTION_STRING environment variable")
        print("Example:")
        print('export AZURE_STORAGE_CONNECTION_STRING="DefaultEndpointsProtocol=https;AccountName=..."')
        return
    
    try:
        # Initialize the deleter
        deleter = AzureBlobDeleter(CONNECTION_STRING, CONTAINER_NAME)
        
        print("🔍 Azure Blob Storage - Vendor Folder Deletion")
        print("=" * 50)
        print(f"Container: {CONTAINER_NAME}")
        print(f"Base Path: {BASE_PATH}")
        print(f"Vendors to delete: {VENDORS_TO_DELETE}")
        print()
        
        # Step 1: List all existing vendor folders
        print("📋 Step 1: Checking existing vendor folders...")
        all_vendor_folders = deleter.list_vendor_folders(BASE_PATH)
        
        if not all_vendor_folders:
            print("❌ No vendor folders found in the specified path.")
            return
        
        print(f"Found {len(all_vendor_folders)} vendor folders:")
        for vendor in all_vendor_folders:
            print(f"   📁 {vendor}")
        print()
        
        # Step 2: Check which vendors to delete actually exist
        existing_vendors_to_delete = [v for v in VENDORS_TO_DELETE if v in all_vendor_folders]
        missing_vendors = [v for v in VENDORS_TO_DELETE if v not in all_vendor_folders]
        
        if missing_vendors:
            print(f"⚠️  Warning: These vendors were not found: {missing_vendors}")
        
        if not existing_vendors_to_delete:
            print("❌ None of the specified vendors exist. Nothing to delete.")
            return
        
        print(f"✅ Found {len(existing_vendors_to_delete)} vendors to delete: {existing_vendors_to_delete}")
        print()
        
        # Step 3: Dry run - show what would be deleted
        print("🔍 Step 3: DRY RUN - Showing what would be deleted...")
        print("-" * 50)
        
        for vendor in existing_vendors_to_delete:
            print(f"\n📁 Vendor: {vendor}")
            deleter.delete_vendor_folder(vendor, BASE_PATH, dry_run=True)
        
        print("\n" + "=" * 50)
        
        # Step 4: Confirmation
        print(f"⚠️  WARNING: This will permanently delete {len(existing_vendors_to_delete)} vendor folders and ALL their contents!")
        print("Vendors to be deleted:")
        for vendor in existing_vendors_to_delete:
            print(f"   🗑️  {vendor}")
        print()
        
        response = input("❓ Do you want to proceed with the deletion? Type 'DELETE' to confirm: ")
        
        if response != 'DELETE':
            print("❌ Deletion cancelled. You must type 'DELETE' exactly to confirm.")
            return
        
        # Step 5: Actual deletion
        print("\n🗑️  Step 5: ACTUAL DELETION - Deleting vendor folders...")
        print("-" * 50)
        
        results = {}
        for vendor in existing_vendors_to_delete:
            print(f"\n🗑️  Deleting vendor: {vendor}")
            success = deleter.delete_vendor_folder(vendor, BASE_PATH, dry_run=False)
            results[vendor] = success
        
        # Step 6: Summary
        print("\n" + "=" * 50)
        print("📊 DELETION SUMMARY")
        print("=" * 50)
        
        successful_deletions = []
        failed_deletions = []
        
        for vendor, success in results.items():
            if success:
                successful_deletions.append(vendor)
                print(f"✅ {vendor}: SUCCESS")
            else:
                failed_deletions.append(vendor)
                print(f"❌ {vendor}: FAILED")
        
        print(f"\n📈 Results:")
        print(f"   ✅ Successfully deleted: {len(successful_deletions)} vendors")
        print(f"   ❌ Failed to delete: {len(failed_deletions)} vendors")
        
        if successful_deletions:
            print(f"   🗑️  Deleted vendors: {successful_deletions}")
        
        if failed_deletions:
            print(f"   ⚠️  Failed vendors: {failed_deletions}")
            print("   💡 Check the logs above for error details.")
        
        print("\n✅ Deletion process completed!")
        
    except Exception as e:
        logger.error(f"❌ An error occurred: {str(e)}")
        print(f"\n❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
